//
//  HybridTranslationServiceTests.swift
//  RockerSTTTests
//
//  Created by Augment Agent on 2025/7/26.
//

import XCTest
@testable import RockerSTT

@MainActor
final class HybridTranslationServiceTests: XCTestCase {
    
    var hybridService: HybridTranslationService!
    var originalService: TranslationService!
    
    override func setUp() {
        super.setUp()
        hybridService = HybridTranslationService()
        originalService = TranslationService()
    }
    
    override func tearDown() {
        hybridService = nil
        originalService = nil
        super.tearDown()
    }
    
    // MARK: - Google Translate Tests
    
    func testGoogleTranslateBasicTranslation() async throws {
        // Test basic English to Spanish translation
        let result = try await hybridService.translate(
            text: "Hello world",
            from: .english,
            to: .spanish
        )
        
        XCTAssertFalse(result.isEmpty, "Translation result should not be empty")
        XCTAssertNotEqual(result, "Hello world", "Translation should be different from original")
        print("✅ Google Translate EN→ES: '\(result)'")
    }
    
    func testGoogleTranslateCantonese() async throws {
        // Test the specific Cantonese text provided by user
        let cantoneseText = "粤语写嘢喺明末清初嗰阵已经有一九二零年代到一九三零年代初亦风行一时由二战时期嘅限于传统左派"
        
        let result = try await hybridService.translate(
            text: cantoneseText,
            from: .cantonese,
            to: .chineseSimplified
        )
        
        XCTAssertFalse(result.isEmpty, "Translation result should not be empty")
        XCTAssertNotEqual(result, cantoneseText, "Translation should be different from original")
        print("✅ Google Translate Cantonese→Simplified Chinese: '\(result)'")
    }
    
    func testGoogleTranslateAutoDetection() async throws {
        // Test auto-detection with English text
        let result = try await hybridService.translateWithAutoDetection(
            text: "Hello world",
            to: .spanish
        )
        
        XCTAssertFalse(result.isEmpty, "Auto-detection translation should not be empty")
        print("✅ Google Translate Auto-detection EN→ES: '\(result)'")
    }
    
    func testGoogleTranslateLanguageDetection() async throws {
        // Test language detection
        let (language, confidence) = try await hybridService.detectLanguage(text: "Hello world")
        
        XCTAssertEqual(language, .english, "Should detect English")
        XCTAssertGreaterThan(confidence, 0.5, "Confidence should be reasonable")
        print("✅ Google Translate Language Detection: \(language.displayName) (confidence: \(confidence))")
    }
    
    func testGoogleTranslateMultipleLanguages() async throws {
        let testCases: [(String, TranslationLanguage, TranslationLanguage)] = [
            ("Hello", .english, .french),
            ("Bonjour", .french, .german),
            ("Hola", .spanish, .italian),
            ("こんにちは", .japanese, .english)
        ]
        
        for (text, from, to) in testCases {
            let result = try await hybridService.translate(text: text, from: from, to: to)
            XCTAssertFalse(result.isEmpty, "Translation from \(from.displayName) to \(to.displayName) should not be empty")
            print("✅ Google Translate \(from.displayName)→\(to.displayName): '\(text)' → '\(result)'")
        }
    }
    
    // MARK: - NLLB Tests
    
    func testNLLBConfiguration() {
        // Test NLLB-only configuration
        let nllbConfig = TranslationServiceConfig.nllbOnly
        XCTAssertEqual(nllbConfig.preferredService, .nllb)
        XCTAssertNil(nllbConfig.fallbackService)
        print("✅ NLLB Configuration: \(nllbConfig.preferredService.displayName)")
    }
    
    func testNLLBLanguageMapping() {
        // Test NLLB language code mapping
        XCTAssertEqual(NLLBLanguageMapper.toNLLB("en"), "eng_Latn")
        XCTAssertEqual(NLLBLanguageMapper.toNLLB("zh-CN"), "zho_Hans")
        XCTAssertEqual(NLLBLanguageMapper.toNLLB("ja"), "jpn_Jpan")
        XCTAssertEqual(NLLBLanguageMapper.toNLLB("yue"), "yue_Hant")
        
        XCTAssertEqual(NLLBLanguageMapper.toGoogle("eng_Latn"), "en")
        XCTAssertEqual(NLLBLanguageMapper.toGoogle("zho_Hans"), "zh-CN")
        XCTAssertEqual(NLLBLanguageMapper.toGoogle("jpn_Jpan"), "ja")
        
        XCTAssertTrue(NLLBLanguageMapper.isNLLBSupported("en"))
        XCTAssertTrue(NLLBLanguageMapper.isNLLBSupported("zh-CN"))
        XCTAssertFalse(NLLBLanguageMapper.isNLLBSupported("unknown"))
        
        print("✅ NLLB Language Mapping: All mappings correct")
    }
    
    func testNLLBTranslationIfServerAvailable() async throws {
        // Create NLLB-only service for testing
        let nllbService = HybridTranslationService()
        
        // Update to NLLB-only configuration
        let nllbConfig = TranslationServiceConfig.nllbOnly
        
        do {
            // Test with a simple English to Spanish translation
            let result = try await nllbService.translate(
                text: "Hello world",
                from: .english,
                to: .spanish
            )
            
            XCTAssertFalse(result.isEmpty, "NLLB translation should not be empty")
            print("✅ NLLB Translation EN→ES: '\(result)'")
            
        } catch TranslationError.serverError(let code) {
            // NLLB server might not be available in test environment
            print("⚠️ NLLB Server not available (HTTP \(code)) - skipping NLLB tests")
            throw XCTSkip("NLLB server not available for testing")
            
        } catch TranslationError.unsupportedLanguage(let message) {
            print("⚠️ NLLB Language not supported: \(message)")
            throw XCTSkip("NLLB language not supported")
        }
    }
    
    // MARK: - Hybrid Service Tests
    
    func testHybridServiceConfiguration() {
        // Test different configurations
        let configs: [TranslationServiceConfig] = [
            .googlePrimary,
            .nllbPrimary,
            .optimalHybrid,
            .asianNLLB,
            .googleOnly,
            .nllbOnly
        ]
        
        for config in configs {
            XCTAssertNotNil(config.preferredService, "Configuration should have preferred service")
            print("✅ Configuration \(config.preferredService.displayName): Valid")
        }
    }
    
    func testHybridServiceLanguageRouting() {
        let optimalConfig = TranslationServiceConfig.optimalHybrid
        
        // Test Asian languages should use NLLB
        XCTAssertEqual(optimalConfig.serviceForLanguage(.japanese), .nllb)
        XCTAssertEqual(optimalConfig.serviceForLanguage(.korean), .nllb)
        XCTAssertEqual(optimalConfig.serviceForLanguage(.chineseSimplified), .nllb)
        XCTAssertEqual(optimalConfig.serviceForLanguage(.cantonese), .nllb)
        
        // Test European languages should use Google
        XCTAssertEqual(optimalConfig.serviceForLanguage(.english), .google)
        XCTAssertEqual(optimalConfig.serviceForLanguage(.spanish), .google)
        XCTAssertEqual(optimalConfig.serviceForLanguage(.french), .google)
        XCTAssertEqual(optimalConfig.serviceForLanguage(.german), .google)
        
        print("✅ Hybrid Service Language Routing: Correct routing for optimal hybrid")
    }
    
    func testHybridServiceFallback() async throws {
        // Test that fallback works when primary service fails
        // This is difficult to test without mocking, but we can test the configuration
        
        let configWithFallback = TranslationServiceConfig.googlePrimary
        XCTAssertEqual(configWithFallback.preferredService, .google)
        XCTAssertEqual(configWithFallback.fallbackService, .nllb)
        
        let configWithoutFallback = TranslationServiceConfig.googleOnly
        XCTAssertEqual(configWithoutFallback.preferredService, .google)
        XCTAssertNil(configWithoutFallback.fallbackService)
        
        print("✅ Hybrid Service Fallback: Configuration correct")
    }
    
    // MARK: - Error Handling Tests
    
    func testEmptyTextError() async {
        do {
            _ = try await hybridService.translate(text: "", from: .english, to: .spanish)
            XCTFail("Should throw error for empty text")
        } catch TranslationError.emptyText {
            print("✅ Empty Text Error: Correctly handled")
        } catch {
            XCTFail("Unexpected error: \(error)")
        }
    }
    
    func testSameLanguageTranslation() async throws {
        let result = try await hybridService.translate(
            text: "Hello world",
            from: .english,
            to: .english
        )
        
        XCTAssertEqual(result, "Hello world", "Same language translation should return original text")
        print("✅ Same Language Translation: Returns original text")
    }
    
    // MARK: - Performance Tests
    
    func testTranslationPerformance() async throws {
        let text = "Hello world, this is a test of translation performance."
        
        let startTime = CFAbsoluteTimeGetCurrent()
        
        _ = try await hybridService.translate(
            text: text,
            from: .english,
            to: .spanish
        )
        
        let timeElapsed = CFAbsoluteTimeGetCurrent() - startTime
        
        XCTAssertLessThan(timeElapsed, 10.0, "Translation should complete within 10 seconds")
        print("✅ Translation Performance: Completed in \(String(format: "%.2f", timeElapsed)) seconds")
    }
}
